
import {<PERSON><PERSON>, Card, Col, Form, Input, Modal, Row, Select, Spin, Switch, Tag} from "antd";
import {CreditCardIcon, IdCardIcon, Mail, PhoneIcon, School, TicketIcon, UserIcon, PercentIcon, CreditCard, SearchIcon, CheckIcon, RefreshCwIcon, PrinterIcon, AlertTriangleIcon} from "lucide-react";
import {LineDisplay} from "../index.ts";
import { formatImagePath } from "../../tools/helpers.ts";
import { useDispatch } from "react-redux";
import { getLineStationsAndRoutes } from "../../features/admin/lineSlice.ts";
import { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { getPaymentMethodsAll } from "../../features/admin/paymentMethodsSlice.ts";
import { getDiscountsAll } from "../../features/admin/discountSlice.ts";
import { getCardFeesAll } from "../../features/admin/cardFeeSlice.ts";
import { processPayment } from "../../features/subs/transactionSlice.ts";

import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import { hasPermission } from "../../helpers/permissions.ts";
import { CheckCircleOutlined, FieldNumberOutlined, SettingFilled, UsergroupAddOutlined } from "@ant-design/icons";
import { getConfigsAll } from "../../features/admin/configSlice.ts";
import { verifySocialAffairCinParent } from "../../features/admin/socialAffairSlice.ts";
import dayjs from 'dayjs';
import { getSubsCardsBySubscription } from "../../features/admin/subsCardSlice.ts";


const PaymentModal:any = ({
paymentModal,
handleReset,
loading: externalLoading,
croppedImage,
abnRecord,
onPaymentSuccess,
isRenewal = false
}:any) => {
    
    /*|--------------------------------------------------------------------------
    |  - PAYMENT SUBS
    |-------------------------------------------------------------------------- */
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;
    const dispatch = useDispatch<any>();
    const [lineDetails, setLineDetails] = useState<any>(null);
    const [isLoadingLineDetails, setIsLoadingLineDetails] = useState(false);
    const [subscriptionAmount, setSubscriptionAmount] = useState<number>(0);
    const [cardFeeAmount, setCardFeeAmount] = useState<number | null>(null);
    const [amountWithCardFee, setAmountWithCardFee] = useState<number | null>(null);
    const [discountedAmount, setDiscountedAmount] = useState<number | null>(null);
    const [applicableDiscount, setApplicableDiscount] = useState<any>(null);
    const [isRegularTariff, setIsRegularTariff] = useState<boolean>(false);
    const [isSocialAffair, setIsSocialAffair] = useState(false);
    const [isSocialAffairVerified, setIsSocialAffairVerified] = useState(false);
    const [socialAffairGovernorate, setSocialAffairGovernorate] = useState<any>(null);
    const [isCustomDiscountEnabled, setIsCustomDiscountEnabled] = useState<boolean>(false);
    const [customDiscountPercentage, setCustomDiscountPercentage] = useState<string>("");
    const [appliedCustomDiscountPercentage, setAppliedCustomDiscountPercentage] = useState<string>("");
    const [previewCustomDiscountAmount, setPreviewCustomDiscountAmount] = useState<number | null>(null);
    const viewMode = abnRecord?.status === "PAYED";
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();

    const discounts = useSelector((state: any) => state.discount.items.data);
    const paymentMethods = useSelector((state: any) => state.paymentMethod.items.data);
    const cardFees = useSelector((state: any) => state.cardFee.items.data);
    const configs = useSelector((state: any) => state.config.items.data);
    const subsCards = useSelector((state: any) => state.subsCard.subscriptionCards?.data);


    const [paymentLoading, setPaymentLoading] = useState(false);
    const [isLoadingCardInfo, setIsLoadingCardInfo] = useState(false);

    /*|--------------------------------------------------------------------------
    | FETCH ALL DATA
    |-------------------------------------------------------------------------- */
    const fetchStoreData = async () => {
            if(!discounts?.length){
                await dispatch(getDiscountsAll()).unwrap()
            }
            if(!paymentMethods?.length){
                await dispatch(getPaymentMethodsAll()).unwrap()
            }
            if(!cardFees?.length){
                await dispatch(getCardFeesAll()).unwrap()
            }
            if(!configs?.length){
                await dispatch(getConfigsAll()).unwrap()
            }
        }

        const fetchSubsCardInfo = async () => {
            if (!abnRecord?.id) return;

            setIsLoadingCardInfo(true);
            try {
                await dispatch(getSubsCardsBySubscription(abnRecord.id)).unwrap();
            } catch (error) {
                console.log(error);
            } finally {
                setIsLoadingCardInfo(false);
            }
        };

        useEffect(() => {
            fetchStoreData()
        }, []);

        useEffect(() => {
            if (abnRecord?.id && abnRecord?.status === "PAYED") {
                fetchSubsCardInfo();
            }
        }, [abnRecord?.id, abnRecord?.status]);

    useEffect(() => {
        if (abnRecord?.trip?.id_line) {
            fetchLineDetails(abnRecord.trip.id_line);
        }
    }, [abnRecord?.trip?.id_line]);

    useEffect(() => {
        if (abnRecord) {
            const calculateAmount = () => {
                let baseAmount = calculateSubscriptionAmount(abnRecord);
                setSubscriptionAmount(baseAmount || 0);

                const cardFee = findCardFee(abnRecord, cardFees);
                const cardFeeValue = cardFee ? parseFloat(cardFee.amount) : 0;
                setCardFeeAmount(cardFeeValue);

                const totalWithCardFee = baseAmount !== null ? baseAmount + cardFeeValue : null;
                setAmountWithCardFee(totalWithCardFee);

                const discount = findApplicableDiscount(abnRecord, discounts);

                setApplicableDiscount(discount);

                const customDiscount = isCustomDiscountEnabled ?
                    calculateCustomDiscount(totalWithCardFee || 0, appliedCustomDiscountPercentage) : 0;

                const finalAmount = applyDiscount(totalWithCardFee, discount, customDiscount);
                setDiscountedAmount(finalAmount);
            };

            calculateAmount();
        }
    }, [abnRecord, discounts, cardFees, isCustomDiscountEnabled, appliedCustomDiscountPercentage]);

    const getStationName = (stationId: number | undefined, isStart = true): string => {
        if (!stationId) return "";

        if (isStart && abnRecord?.trip?.station_start?.[`nom_${currentLang}`]) {
            return abnRecord.trip.station_start?.[`nom_${currentLang}`];
        }
        if (!isStart && abnRecord?.trip?.station_end?.[`nom_${currentLang}`]) {
            return abnRecord.trip.station_end?.[`nom_${currentLang}`];
        }

        if (lineDetails?.stations) {
            const station = lineDetails.stations.find((s: any) => s.id === stationId);
            if (station) {
                return station?.[`nom_${currentLang}`] || station.name || stationId.toString();
            }
        }

        return stationId.toString();
    };

    const fetchLineDetails = async (lineId: number) => {
        if (!lineId) {
            setLineDetails(null);
            return;
        }
        try {
            setIsLoadingLineDetails(true);
            const response = await dispatch(getLineStationsAndRoutes(lineId)).unwrap();

            let lineData = null;
            if (response && response.data) {
                lineData = response.data;
            } else if (response) {
                lineData = response;
            }

            setLineDetails(lineData);
        } catch (error) {
            console.error('Error fetching line details:', error);
            setLineDetails(null);
        } finally {
            setIsLoadingLineDetails(false);
        }
    };
    
    /*|--------------------------------------------------------------------------
    | CALCULATE SUBSCRIPTION AMOUNT
    |-------------------------------------------------------------------------- */
    const calculateSubscriptionAmount = (abnRecord: any) => {
        if (!abnRecord?.trip?.tariff_options || abnRecord.trip.tariff_options.length === 0) {
            // console.log('No tariff options found');
            setIsRegularTariff(false);
            return null;
        }

        const tariffOption = abnRecord.trip.tariff_options.find(
            (option: any) => option.id_subs_type === abnRecord.id_subs_type
        );

        if (!tariffOption) {
            // console.log('No matching tariff option found for subscription type:', abnRecord.id_subs_type);
            setIsRegularTariff(false);
            return null;
        }

        setIsRegularTariff(!!tariffOption.is_regular);

        const numberOfKm = abnRecord.trip.number_of_km;
        let base_amount;

        if (tariffOption.is_regular) {
            const tariffPerKm = parseFloat(tariffOption.tariff_base.tariffPerKM);
            base_amount = numberOfKm * tariffPerKm;
        } else {
            const manualTariff = tariffOption.manual_tariff ? parseFloat(tariffOption.manual_tariff) : null;
            base_amount = manualTariff;
        }


        if (!base_amount) return null;

        const periodicity = abnRecord?.periodicity?.periodicity_code;
        const isStudent = abnRecord?.subs_type?.is_student;

        const hasVacances = isStudent ? abnRecord?.hasVacances : null;
        const restDays = !isStudent ? (Array.isArray(abnRecord?.rest_days) ? abnRecord.rest_days.length : 0) : 0;

        if (isStudent) {
            switch (periodicity) {
                case 'SEMESTRIEL':
                    if (hasVacances) {
                        return (base_amount * 1.5 * 3) / 2;
                    } else {
                        return (base_amount * 3) / 2;
                    }
                case 'YEARLY':
                    if (hasVacances) {
                        return base_amount * 1.5 * 3;
                    } else {
                        return base_amount * 3;
                    }
                case 'TRIMESTRIEL':
                    if (hasVacances && abnRecord?.id_parent === null) {
                        // This should come from configuration
                        // applied only for 2 and 3rd TRIMESTRE for new subs
                        const majoration = configs?.find((config:any) => config.key === "majoration")

                        // get current month
                        const currentMonth = new Date().getMonth() + 1;

                        // if current month is in second or third trimester
                        if (currentMonth >= 1 && currentMonth <= 6) {
                            // Store majoration info for display in payment details
                            abnRecord.majoration = {
                                applied: true,
                                value: majoration?.value || 0,
                                original_amount: base_amount,
                                majored_amount: base_amount * majoration?.value
                            };
                            return base_amount * majoration?.value;
                        }

                        return base_amount;
                    } else {
                        return base_amount;
                    }
                default:
                    return base_amount;
            }
        }

        else {

            let workingDaysInMonth = 30;

            if(restDays === 1) {
                workingDaysInMonth = 25;
            } else if(restDays === 2) {
                workingDaysInMonth = 22;
            }
            

            switch (periodicity) {
                case 'SEMESTRIEL':
                    return (base_amount * 2 * workingDaysInMonth) * 6;
                case 'YEARLY':
                    return (base_amount * 2 * workingDaysInMonth) * 12;
                case 'MONTHLY':
                    return (base_amount * 2 * workingDaysInMonth);
                case 'TRIMESTRIEL':
                    console.log('Final amount:', (base_amount * 2 * workingDaysInMonth) * 3);
                    return (base_amount * 2 * workingDaysInMonth) * 3;
                default:
                    return base_amount;
            }


        }

        
        
    };


    /*|--------------------------------------------------------------------------
    | FIND APPLICABLE DISCOUNT
    |-------------------------------------------------------------------------- */
    const findApplicableDiscount = (abnRecord: any, discountsList: any[]) => {
        if (!abnRecord?.subs_type?.id || !abnRecord?.periodicity?.id || !discountsList.length) {
            //console.log('Missing subs type, periodicity, or discounts list');
            return null;
        }

        const matchingDiscount = discountsList.find(discount => {
            console.log(discount);

            const abnTypeMatches = discount?.subs_type?.id === abnRecord?.subs_type?.id;
            const periodicityMatches = discount.periodicities?.some(
                (p: any) => p.id === abnRecord.periodicity.id
            );
            const specialClientMatches = discount.special_client === abnRecord?.special_client;
            const isStagiaireMatches = discount.is_stagiaire === abnRecord?.is_stagiaire;

            const now = dayjs();
            const startDate = dayjs(discount.date_start);
            const endDate = dayjs(discount.date_end);

            const currentMonthDay = now.format('MM-DD');
            const startMonthDay = startDate.format('MM-DD');
            const endMonthDay = endDate.format('MM-DD');

            const isDateValid = currentMonthDay >= startMonthDay && currentMonthDay <= endMonthDay;

            return (abnTypeMatches && periodicityMatches && isDateValid) || specialClientMatches || isStagiaireMatches;
        });
        return matchingDiscount || null;
    };


    /*|--------------------------------------------------------------------------
    | FIND CARD FEE
    |-------------------------------------------------------------------------- */
    const findCardFee = (abnRecord: any, cardFeesList: any[]) => {
        if (!abnRecord?.id_subs_type || !cardFeesList?.length) {
            console.log('Missing subscription type or card fees list');
            return null;
        }

        const matchingCardFee = cardFeesList.find(cardFee => {
            return cardFee.id_subs_type === abnRecord.id_subs_type;
        });

        console.log('Found matching card fee:', matchingCardFee);
        return matchingCardFee || null;
    };




    const calculateCustomDiscount = (amount : number, customPercentage : string) => {
        if (!customPercentage || amount <= 0) {
            return 0;
        }

        const customDiscountPercent = parseFloat(customPercentage);
        if (isNaN(customDiscountPercent) || customDiscountPercent <= 0) {
            return 0;
        }

        // Calculate the discount amount based on the percentage
        const discountValue = (amount * customDiscountPercent) / 100;

        // Make sure the custom discount doesn't exceed the total amount
        return Math.min(discountValue, amount);
    }

    /*|--------------------------------------------------------------------------
    | APPLY DISCOUNT
    |-------------------------------------------------------------------------- */
    const applyDiscount = (amount: number | null, discount: any, customDiscountValue: number = 0) => {
        if (amount === null) {
            return amount;
        }

        let totalDiscount = 0;

        // Calculate custom discount (applied on total amount)
        if (customDiscountValue > 0) {
            totalDiscount += customDiscountValue;
        }

        // Calculate general discount (applied on total amount)
        if (discount && discount.percentage) {
            const discountPercentage = parseFloat(discount.percentage);
            if (!isNaN(discountPercentage)) {
                totalDiscount += (amount * discountPercentage / 100);
            }
        }

        // Apply total discount
        const discounted = amount - totalDiscount;

        // Ensure the final amount is not negative
        return Math.max(0, discounted);
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE PAYMENT
    |-------------------------------------------------------------------------- */
    const handlePayment = async (values: any) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            setPaymentLoading(true);

            const finalAmount = getFinalAmount;
            console.log(finalAmount);

            if (finalAmount === null || !abnRecord?.id || !abnRecord?.client?.id) {
                toast.update(toastId, {
                    render: t("messages.error"),
                    type: "error",
                    isLoading: false,
                    autoClose: 3000
                });
                return;
            }

            // payment details
            const paymentDetails = {
                tariff_base: abnRecord?.trip?.tariff_options?.find(
                    (option: any) => option.id_subs_type === abnRecord.id_subs_type
                )?.tariff_base?.tariffPerKM || 0,
                number_of_km: abnRecord?.trip?.number_of_km || 0,
                is_social_affair: isSocialAffairVerified ? 1 : 0,
                amount_base: subscriptionAmount?.toFixed(3) || 0,
                periodicity: abnRecord?.periodicity?.periodicity_code || '',
                discount_percentage: applicableDiscount?.percentage || 0,
                discount_amount: generalDiscountAmount?.toFixed(3) || 0,
                card_fee: cardFeeAmount || 0,
                total_amount_without_discount: amountWithCardFee || 0,
                total_amount_with_discount: getFinalAmount?.toFixed(3),
                // Include custom discount info if it exists
                ...(isCustomDiscountEnabled && appliedCustomDiscountPercentage ? {
                    custom_discount_percentage: parseFloat(appliedCustomDiscountPercentage).toFixed(2),
                    custom_discount_amount: customDiscountAmount?.toFixed(3) || 0,
                } : {}),
                // Include majoration info if it exists
                ...(abnRecord?.majoration?.applied ? { majoration: abnRecord.majoration } : {})
            };

            const paymentPayload = {
                transaction: {
                    subscription_id: abnRecord.id,
                    client_id: abnRecord.client.id,
                    governorate_id : socialAffairGovernorate?.id,
                    amount: parseFloat(finalAmount.toFixed(1)).toFixed(3),
                    payment_date: new Date().toISOString().split('T')[0],
                    payment_mode: isSocialAffairVerified ? 'social_affair' : 'guichet',
                    payment_method_id: values.payment_method_id || null,
                    status: 'completed',
                    transaction_reference: `SUBS-${abnRecord.id}-${Date.now()}`,
                    notes: (() => {
                        let notes = [];
                        if (applicableDiscount) {
                            notes.push(`Remise générale: ${applicableDiscount.percentage}% (${generalDiscountAmount?.toFixed(3) || 0} TND)`);
                        }
                        if (isCustomDiscountEnabled && appliedCustomDiscountPercentage) {
                            notes.push(`Remise personnalisée: ${parseFloat(appliedCustomDiscountPercentage).toFixed(2)}% (${customDiscountAmount?.toFixed(3) || 0} TND)`);
                        }
                        return notes.join(', ');
                    })(),
                    payment_details: paymentDetails
                },
                subscription: {
                    id: abnRecord.id,
                    is_social_affair: isSocialAffairVerified ? 1 : 0,
                    status: 'PAYED',
                    ...(isRenewal && abnRecord.id_parent ? { id_parent: abnRecord.id_parent } : {})
                }
            };

            const response = await dispatch(processPayment(paymentPayload)).unwrap();

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });

            if (onPaymentSuccess && typeof onPaymentSuccess === 'function') {
                onPaymentSuccess(response.data);
            }
            handleReset();
        } catch (error: any) {
            console.error('Payment error:', error);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setPaymentLoading(false);
        }
    }

    const confirmPayment = (values: any) => {

    const modal = Modal.confirm({
            title:t("manage_newSubs.confirmPaymentTitle", "Confirmer le paiement"),
            content: t("manage_newSubs.confirmPaymentMessage", "Êtes-vous sûr de vouloir confirmer ce paiement ?"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handlePayment(values);
            },
            centered: true,
        });
    };

    // Format the subscription amount for display
    const formattedBaseAmount = useMemo(() => {
        if (subscriptionAmount === null) return "Calcul en cours...";
        return `${subscriptionAmount.toFixed(3)} TND`;
    }, [subscriptionAmount]);

    // Format the card fee amount for display
    const formattedCardFeeAmount = useMemo(() => {
        if (cardFeeAmount === null) return "0.00 TND";
        return `${cardFeeAmount?.toFixed(3)} TND`;
    }, [cardFeeAmount]);



    // Get final amount value
    const getFinalAmount = useMemo(() => {
        return discountedAmount !== null ? discountedAmount : amountWithCardFee;
    }, [discountedAmount, amountWithCardFee]);

    // Calculate general discount amount for display (without custom discount)
    const generalDiscountAmount = useMemo(() => {
        if (!applicableDiscount || amountWithCardFee === null) return null;

        const discountPercentage = parseFloat(applicableDiscount.percentage);
        if (isNaN(discountPercentage)) return null;

        return (amountWithCardFee * discountPercentage / 100);
    }, [applicableDiscount, amountWithCardFee]);

    // Format the general discount amount for display
    const formattedDiscountAmount = useMemo(() => {
        if (generalDiscountAmount === null) return "0.00 TND";
        return `${generalDiscountAmount.toFixed(3)} TND`;
    }, [generalDiscountAmount]);

    // Calculate custom discount amount for display
    const customDiscountAmount = useMemo(() => {
        if (!isCustomDiscountEnabled || !appliedCustomDiscountPercentage || amountWithCardFee === null) return null;

        const customPercent = parseFloat(appliedCustomDiscountPercentage);
        if (isNaN(customPercent)) return null;

        // Calculate on total amount before general discount
        return (amountWithCardFee * customPercent / 100);
    }, [isCustomDiscountEnabled, appliedCustomDiscountPercentage, amountWithCardFee]);

    // Get discount percentage for display
    const discountPercentage = useMemo(() => {
        if (!applicableDiscount) return null;
        return applicableDiscount.percentage;
    }, [applicableDiscount]);

    // Format the final amount for display
    const formattedFinalAmount = useMemo(() => {
        const finalAmount = getFinalAmount;
        if (finalAmount === null) return "0.00 TND";
        return `${parseFloat(finalAmount.toFixed(1)).toFixed(3)} TND`;
    }, [getFinalAmount]);

    useEffect(() => {
        if (abnRecord?.latestTransaction?.payment_method_id) {
            console.log(abnRecord.latestTransaction.payment_method_id);

            form.setFieldsValue({
                payment_method_id: abnRecord.latestTransaction.payment_method_id
            });
        } else {
            form.resetFields();
        }
    }, [abnRecord, form]);

    // Update preview discount amount when typing
    useEffect(() => {
        if (isCustomDiscountEnabled && customDiscountPercentage && amountWithCardFee !== null) {
            const customPercent = parseFloat(customDiscountPercentage);
            if (!isNaN(customPercent) && customPercent > 0) {
                const previewAmount = (amountWithCardFee * customPercent / 100);
                setPreviewCustomDiscountAmount(previewAmount);
            } else {
                setPreviewCustomDiscountAmount(0);
            }
        } else {
            setPreviewCustomDiscountAmount(null);
        }
    }, [isCustomDiscountEnabled, customDiscountPercentage, amountWithCardFee]);


    /*|--------------------------------------------------------------------------
    |  - HANDLE verify
    |-------------------------------------------------------------------------- */
    const handleVerify = async () => {
            const toastId = toast.loading(t("common.verifying"), {
                position: 'top-center',
            });

            const final_amount = getFinalAmount || 0;

            try {
                //const values = await form.validateFields(['identifier', 'dob_search']);
                const values = await form.validateFields(['cin_parent']);
                /*
                let eleve_etudiant: "eleve" | "etudiant" = "eleve";

                if (selectedClient) {
                    if (selectedClient?.client_type?.is_student && selectedClient?.client_type?.hasCIN) {
                        eleve_etudiant = "etudiant";
                    } else if (selectedClient?.client_type?.is_student && !selectedClient?.client_type?.hasCIN) {
                        eleve_etudiant = "eleve";
                    } else if (selectedClient?.client_type?.hasCIN) {
                        eleve_etudiant = "etudiant";
                    }
                }
                */
                const response = await dispatch(verifySocialAffairCinParent({
                    cin_parent: values.cin_parent,
                    final_amount: final_amount,
                    //dob: dayjs(values.dob_search).format('YYYY-MM-DD'),
                    //eleve_etudiant: eleve_etudiant
                })).unwrap();

                if(response.exists && response.has_purchase_order){
                    setIsSocialAffair(false);
                    setIsSocialAffairVerified(true);
                    form.setFieldsValue({ is_social_affair: true });
                    toast.update(toastId, {
                        render: t("messages.found"),
                        type: "success",
                        isLoading: false,
                        autoClose: false,
                        closeOnClick: true
                    });
                    setSocialAffairGovernorate(response.governorate_data);
                } else if(response.exists && !response.has_purchase_order){
                    setIsSocialAffair(false);
                    setIsSocialAffairVerified(false);
                    form.setFieldsValue({
                        is_social_affair: false,
                        cin_parent: null
                    });
                    toast.update(toastId, {
                        render: t("messages.no_purchase_order"),
                        type: "warning",
                        isLoading: false,
                        autoClose: false,
                        closeOnClick: true
                    });
                    setSocialAffairGovernorate(response.governorate_data);
                } else {
                    setIsSocialAffair(false);
                    setIsSocialAffairVerified(false);
                    form.resetFields();
                    toast.update(toastId, {
                        render: t("messages.not_found"),
                        type: "error",
                        isLoading: false,
                        autoClose: false,
                        closeOnClick: true
                    });
                    form.resetFields();
                    setSocialAffairGovernorate(response.governorate_data);
                }
            } catch (error: any) {
                setIsSocialAffair(false);
                form.setFieldsValue({ is_social_affair: false });
                form.resetFields();
                toast.update(toastId, {
                    render: t(`messages.${error.message}`),
                    type: "error",
                    isLoading: false,
                    autoClose: 5000,
                    closeOnClick: true
                });
            } finally {
                setLoading(false);
            }
        }

    const handleApplyCustomDiscount = () => {
        if (isCustomDiscountEnabled && customDiscountPercentage) {
            setAppliedCustomDiscountPercentage(customDiscountPercentage);
        }
    };

    const handleResetPayment = () => {
        handleReset();
        setIsSocialAffairVerified(false);
        setIsSocialAffair(false);
        setIsCustomDiscountEnabled(false);
        setCustomDiscountPercentage("");
        setAppliedCustomDiscountPercentage("");
        setPreviewCustomDiscountAmount(null);
        form.resetFields();
    };

    return (
        <>
            <Modal
                width={1300}
                className="payment-modal"
                title={
                    <div className="flex items-center gap-3">
                        <div className={`p-2.5 rounded-lg shadow-sm transition-all duration-300 transform hover:scale-105 ${abnRecord?.status === "PAYED" ? "bg-gradient-to-r from-green-50 to-green-100" : "bg-gradient-to-r from-red-50 to-red-100"}`}>
                            <CreditCardIcon className={`w-5 h-5 ${abnRecord?.status === "PAYED" ? "text-green-600" : "text-red-600"}`} />
                        </div>
                        <span className="text-xl font-medium tracking-tight">{t("manage_newSubs.detailsAndPayment", "Détails et Paiement")}</span>
                    </div>
                }
                open={paymentModal}
                onCancel={handleResetPayment}
                footer={false}
            >
            <Spin spinning={externalLoading || isLoadingLineDetails}>
                <div className="space-y-2 relative">
                    <div className="absolute top-[190px] right-[24px] transform rotate-10 z-10">
                        <div className={`w-28 h-28 flex items-center justify-center rounded-full ${
                            abnRecord?.status === "PAYED"
                                ? "bg-gradient-to-br from-green-50 to-green-100 border-2 border-green-500"
                                : abnRecord?.status === "PENDING"
                                    ? "bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-500"
                                    : "bg-gradient-to-br from-red-50 to-red-100 border-2 border-red-500"
                        }`}>
                            <div className={`w-24 h-24 rounded-full flex items-center justify-center ${
                                abnRecord?.status === "PAYED"
                                    ? "bg-green-50 border border-green-200"
                                    : abnRecord?.status === "PENDING"
                                        ? "bg-gray-50 border border-gray-200"
                                        : "bg-red-50 border border-red-200"
                            }`}>
                                <div className="text-center">
                                    <span className={`block text-sm font-bold uppercase tracking-widest ${
                                        abnRecord?.status === "PAYED"
                                            ? "text-green-700"
                                            : abnRecord?.status === "PENDING"
                                                ? "text-gray-700"
                                                : "text-red-700"
                                    }`}>
                                    {abnRecord?.status === "NOTPAYED" ? "Non Payé" : abnRecord?.status === "PAYED" ? "Payé" : "En Attente"}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Section Client */}
                    <div className="p-6 bg-white rounded-2xl border border-gray-200 w-full overflow-hidden">
                        <div className="flex flex-col md:flex-row gap-6 relative">
                            <div className="relative group">
                                <div className="w-24 h-24 md:w-[150px] md:h-[150px] flex items-center justify-center rounded-2xl border border-gray-200 bg-white shadow-sm relative overflow-hidden duration-300 hover:-translate-y-0.5">
                                    {croppedImage ? (
                                        <img src={croppedImage} alt="Cropped"
                                             className="w-full h-full object-cover transition-transform duration-300"/>
                                    ) : abnRecord?.photo ? (
                                        <img src={formatImagePath(abnRecord.photo)} alt="Subscription photo"
                                             className="w-full h-full object-cover transition-transform duration-300"/>
                                    ) : (
                                        <div className="flex items-center justify-center w-full h-full bg-gradient-to-br from-gray-50 to-gray-100">
                                            <UserIcon className="w-12 h-12 text-gray-300"/>
                                        </div>
                                    )}
                                </div>
                            </div>
                            {abnRecord?.client && (
                                <div className="flex-1 space-y-4">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                            <div className="p-2.5 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 text-gray-600 mr-3 shadow-sm">
                                                <UserIcon className="w-5 h-5"/>
                                            </div>
                                            {t("manage_newSubs.clientInformation", "Informations Client")}
                                        </h3>
                                        <Tag
                                            color={"default"}
                                            className="font-medium px-4 py-1.5 bg-gradient-to-r from-gray-50 to-gray-100 border-0 text-gray-700 shadow-sm"
                                        >
                                            {abnRecord?.subs_type?.[`nom_${currentLang}`]}
                                        </Tag>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                                        {abnRecord?.subs_type?.is_student && !abnRecord?.subs_type?.hasCIN || abnRecord?.special_client === "SCOLAIRE" ? (
                                            <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                    <IdCardIcon className="font-semibold text-gray-800"/>
                                                </div>
                                                <div>
                                                    <span className="text-xs font-medium text-gray-500">{t("manage_newSubs.labels.matriculate")}</span>
                                                    <p className="font-semibold text-gray-800">{abnRecord?.client?.identity_number}</p>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                    <IdCardIcon className="font-semibold text-gray-800"/>
                                                </div>
                                                <div>
                                                    <span className="text-xs font-medium text-gray-500">{t("manage_newSubs.labels.cin")}</span>
                                                    <p className="font-semibold text-gray-800">{abnRecord?.client?.identity_number}</p>
                                                </div>
                                            </div>
                                        )}

                                        {
                                            abnRecord?.client?.is_moral ? (
                                                <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                    <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                        <UserIcon className="font-semibold text-gray-800"/>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs font-medium text-gray-500">{t("manage_users.labels.societyName")}</span>
                                                        <p className="font-semibold text-gray-800">{abnRecord?.client?.society_name}</p>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                    <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                        <UserIcon className="font-semibold text-gray-800"/>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.clientName")}</span>
                                                        <p className="font-semibold text-gray-800">{abnRecord?.client?.lastname} {abnRecord?.client?.firstname}</p>
                                                    </div>
                                                </div>
                                            )
                                        }

                                        {abnRecord?.subs_type?.is_student && (
                                            <>
                                                <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                    <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                        <School className="font-semibold text-gray-800"/>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.establishment")}</span>
                                                        <p className="font-semibold text-gray-800">{abnRecord?.client?.establishment?.[`nom_${currentLang}`]}</p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                    <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                        <School className="font-semibold text-gray-800"/>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.schoolDegree")}</span>
                                                        <p className="font-semibold text-gray-800">{abnRecord?.client?.degree?.[`nom_${currentLang}`]}</p>
                                                    </div>
                                                </div>
                                            </>
                                        )}

                                        {
                                            abnRecord?.client?.is_moral  ? (
                                                <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                    <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                        <UserIcon className="font-semibold text-gray-800"/>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500">{t("manage_users.labels.legalRepresentative")}</span>
                                                        <p className="font-semibold text-gray-800">{abnRecord?.client?.legal_representative}</p>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                    <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                        <UserIcon className="font-semibold text-gray-800"/>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.dob")}</span>
                                                        <p className="font-semibold text-gray-800">{abnRecord?.client?.dob ? new Date(abnRecord?.client?.dob).toLocaleDateString() : ""}</p>
                                                    </div>
                                                </div>
                                            )
                                        }

                                        <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                            <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                <PhoneIcon className="font-semibold text-gray-800"/>
                                            </div>
                                            <div>
                                                <span className="text-xs text-gray-500">{t("manage_newSubs.labels.phone")}</span>
                                                <p className="font-semibold text-gray-800">{abnRecord?.client.phone}</p>
                                            </div>
                                        </div>

                                        {
                                            !(abnRecord?.subs_type?.is_conventional || abnRecord?.subs_type?.is_impersonal) && (
                                                <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 transition-colors shadow-sm">
                                                <div className="p-2 rounded-lg bg-gray-100 mr-3 shadow-sm">
                                                    <SettingFilled className="font-semibold text-gray-800" style={{ fontSize: '16px' }}/>
                                                </div>
                                                <div>
                                                    <span className="text-xs text-gray-500">{t("manage_newSubs.labels.stagiaire")}</span>
                                                    <p className="font-semibold text-gray-800">{abnRecord?.is_stagiaire ? "Oui" : "Non"}</p>
                                                </div>
                                            </div>
                                            )
                                        }
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Section Abonnement */}
                    <div className="p-6 bg-white rounded-2xl border border-gray-200 w-full">
                        <div className="flex items-center justify-between mb-5">
                            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                <div className="p-2.5 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 text-gray-600 mr-3">
                                    <TicketIcon className="w-5 h-5"/>
                                </div>
                                {t("manage_newSubs.subscriptionDetails", "Détails de l'Abonnement")}
                            </h3>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
                            <div className="col-span-1 md:col-span-2 flex flex-col md:flex-row gap-4">
                                <div className="flex-1 p-4 rounded-xl bg-gradient-to-r from-green-50 to-green-100 border border-green-200 shadow-sm duration-300 transform hover:-translate-y-1">
                                    <div className="flex items-center mb-2">
                                        <div className="p-2 rounded-lg bg-white shadow-sm mr-3">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-600">
                                                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M12 8V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </div>
                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.station_depart")}</span>
                                    </div>
                                    <p className="font-semibold text-green-900 ml-9 mt-1">
                                        {getStationName(abnRecord?.trip?.id_station_start, true)}
                                    </p>
                                </div>

                                <div className="flex-1 p-4 rounded-xl bg-gradient-to-r from-red-50 to-red-100 border border-red-200 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                                    <div className="flex items-center mb-2">
                                        <div className="p-2 rounded-lg bg-white shadow-sm mr-3">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-red-600">
                                                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </div>
                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.station_arrival")}</span>
                                    </div>
                                    <p className="font-semibold text-red-900 ml-9 mt-1">
                                        {getStationName(abnRecord?.trip?.id_station_end, false)}
                                    </p>
                                </div>
                            </div>

                            <div className="p-4 rounded-xl bg-gray-50 border border-gray-100 hover:bg-gray-100 transition-colors">
                                <div className="flex items-center mb-2">
                                    <div className="p-1.5 rounded-md bg-blue-100 mr-2">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-600">
                                            <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <span className="text-xs text-gray-500">{t("manage_newSubs.labels.line")}</span>
                                </div>
                                <p className="font-medium text-gray-800 ml-7">
                                    {lineDetails?.code_line || abnRecord?.trip?.line?.code_line || ""}: {lineDetails?.[`nom_${currentLang}`] || abnRecord?.trip?.line?.[`nom_${currentLang}`] || abnRecord?.trip?.[`nom_${currentLang}`] || ""}
                                </p>
                            </div>

                            <div className="p-4 rounded-xl bg-gray-50 border border-gray-100 hover:bg-gray-100 transition-colors">
                                <div className="flex items-center mb-2">
                                    <div className="p-1.5 rounded-md bg-purple-100 mr-2">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-purple-600">
                                            <path d="M12 2V6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M12 18V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M4.93 4.93L7.76 7.76" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M16.24 16.24L19.07 19.07" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M2 12H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M18 12H22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M4.93 19.07L7.76 16.24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M16.24 7.76L19.07 4.93" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <span className="text-xs text-gray-500">{t("manage_newSubs.labels.periodicity")}</span>
                                </div>
                                <p className="font-medium text-gray-800 ml-7">
                                    {abnRecord?.periodicity?.[`nom_${currentLang}`]}
                                </p>
                            </div>

                            {!abnRecord?.subs_type?.is_student && abnRecord?.rest_days && (
                                <div className="p-4 rounded-xl bg-gray-50 border border-gray-100 hover:bg-gray-100 transition-colors">
                                    <div className="flex items-center mb-2">
                                        <div className="p-1.5 rounded-md bg-amber-100 mr-2">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-amber-600">
                                                <path d="M19 4H5C3.89543 4 3 4.89543 3 6V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V6C21 4.89543 20.1046 4 19 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M16 2V6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M8 2V6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M3 10H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </div>
                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.restDays")}</span>
                                    </div>
                                    <p className="font-medium text-gray-800 ml-7">
                                        {abnRecord?.rest_days?.length} {t("manage_newSubs.days")}
                                    </p>
                                </div>
                            )}

                            {abnRecord?.subs_type?.is_student && (
                                <div className="p-4 rounded-xl bg-gray-50 border border-gray-100 hover:bg-gray-100 transition-colors">
                                    <div className="flex items-center mb-2">
                                        <div className="p-1.5 rounded-md bg-amber-100 mr-2">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-amber-600">
                                                <path d="M12 3V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M17 8L12 3L7 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M19 21H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </div>
                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.vacation")}</span>
                                    </div>
                                    <p className="font-medium text-gray-800 ml-7">
                                        {abnRecord?.hasVacances ? t("manage_newSubs.labels.withVacation") : t("manage_newSubs.labels.withoutVacation")}
                                    </p>
                                </div>
                            )}

                            {abnRecord?.subs_type?.is_impersonal && (
                                <div className="p-4 rounded-xl bg-gray-50 border border-gray-100 hover:bg-gray-100 transition-colors">
                                    <div className="flex items-center mb-2">
                                        <div className="p-1.5 rounded-md bg-amber-100 mr-2">
                                            <FieldNumberOutlined className="text-gray-600" style={{ fontSize: '16px' }}/>
                                        </div>
                                        <span className="text-xs text-gray-500">{t("manage_newSubs.labels.subsNumber")}</span>
                                    </div>
                                    <p className="font-medium text-gray-800 ml-7">
                                        {abnRecord?.subs_number}
                                    </p>
                                </div>
                            )}

                            <div className="p-4 rounded-xl bg-gray-50 border border-gray-100 hover:bg-gray-100 transition-colors">
                                <div className="flex items-center mb-2">
                                    <div className="p-1.5 rounded-md bg-gray-100 mr-2">
                                        <UsergroupAddOutlined className="text-gray-600" style={{ fontSize: '16px' }}/>
                                    </div>
                                    <span className="text-xs text-gray-500">{t("manage_newSubs.labels.socialAffair")}</span>
                                </div>
                                <div className="ml-7">
                                    {abnRecord?.is_social_affair ? (
                                        <Tag color="success" className="rounded-full px-3 py-1 border-0 bg-green-50 text-green-700">
                                            {t("common.yes")}
                                        </Tag>
                                    ) : (
                                        <Tag color="error" className="rounded-full px-3 py-1 border-0 bg-red-50 text-red-700">
                                            {t("common.no")}
                                        </Tag>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="mt-2 mb-2 bg-gray-50 p-4 rounded-xl border border-gray-100">
                            <div className="flex flex-col">
                                {isLoadingLineDetails ? (
                                    <div className="flex justify-center py-8">
                                        <Spin tip={t("common.loading")} />
                                    </div>
                                ) : lineDetails ? (
                                    <LineDisplay
                                        record={lineDetails}
                                        isReversed={abnRecord?.is_reversed}
                                        selectedStationDepart={abnRecord?.trip?.id_station_start}
                                        selectedStationArrival={abnRecord?.trip?.id_station_end}
                                    />
                                ) : (
                                    <div className="text-center py-4 text-gray-500">
                                        {t("common.noData")}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>


                    {/* Section Carte d'abonnement */}
                    {(abnRecord?.status === "PAYED" || abnRecord?.status === "PENDING") && (
                        <div className="px-6 pt-6 pb-6 bg-white rounded-xl border border-gray-200 ">
                            <div className="flex items-center justify-between mb-3">
                                 <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                    <div className={`p-2 rounded-lg mr-3 !bg-purple-100 text-purple-600`}>
                                        <IdCardIcon className="w-5 h-5"/>
                                    </div>
                                    <span className="text-gray-800">
                                            {t("manage_newSubs.cardDetails", "Détails des impressions")}
                                    </span>
                                </h3>

                                <Tag color="blue" className="!m-0 !h-6 !flex !items-center">
                                    <span className="font-medium">{subsCards?.length || 0} carte(s)</span>
                                </Tag>
                            </div>

                            {isLoadingCardInfo ? (
                                <div className="flex justify-center items-center py-3 bg-white rounded-md border border-gray-200">
                                    <Spin size="small" />
                                    <span className="ml-2 text-gray-500">{t("common.loading", "Chargement...")}</span>
                                </div>
                            ) : subsCards && subsCards && subsCards?.length > 0 ? (
                                <div className="flex flex-nowrap scrollable-steps-container pb-3 -mx-1 px-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
                                    {
                                        subsCards?.map((card: any, index: number) => {
                                            console.log(subsCards);

                                            const isImpression = !card.id_motif_duplicate;
                                            const isReimpression = card.motif_duplicate?.id_card_type === 1 && card.motif_duplicate;
                                            const isDuplicata = card.motif_duplicate && !isReimpression;

                                            let bgColor = 'bg-white';
                                            let borderColor = 'border-gray-200';
                                            let tagColor = 'purple';
                                            let iconComponent = <PrinterIcon className="w-3 h-3" />;

                                            if (isImpression) {
                                                bgColor = index === 0 ? 'bg-purple-50' : 'bg-white';
                                                borderColor = 'border-purple-200';
                                                tagColor = 'purple';
                                                iconComponent = <PrinterIcon className="w-3 h-3" />;
                                            } else if (isReimpression) {
                                                bgColor = 'bg-blue-50';
                                                borderColor = 'border-blue-200';
                                                tagColor = 'blue';
                                                iconComponent = <RefreshCwIcon className="w-3 h-3" />;
                                            } else {
                                                bgColor = 'bg-orange-50';
                                                borderColor = 'border-orange-200';
                                                tagColor = 'orange';
                                                iconComponent = <AlertTriangleIcon className="w-3 h-3" />;
                                            }

                                            return (
                                                <div key={card.id} className={`flex-shrink-0 w-[200px] p-2.5 rounded-md ${bgColor} border ${borderColor} mr-2`}>
                                                    <div className="flex flex-col h-full">
                                                        <div className="flex justify-between items-center mb-1.5">
                                                            <Tag className="!m-0 !px-2 !py-0.5 !text-xs !h-6" color={tagColor}>
                                                                <div className="flex items-center gap-1">
                                                                    {iconComponent}
                                                                    <span className="font-medium">
                                                                        {isImpression
                                                                            ? t("common.impression", "Impression")
                                                                            : isReimpression
                                                                                ? t("common.reimpression", "Réimpression")
                                                                                : t("common.duplicata", "Duplicata")
                                                                        }
                                                                    </span>
                                                                </div>
                                                            </Tag>
                                                            <span className="text-xs text-gray-500 bg-white px-1.5 py-0.5 rounded-full border border-gray-100">
                                                                {dayjs(card.created_at).format('DD/MM/YY')}
                                                            </span>
                                                        </div>

                                                        <div className="text-sm font-medium text-gray-800 truncate bg-white px-2 py-1 rounded-md border border-gray-100 mb-1.5">
                                                            {card.card_number || `${card?.card_type?.code}-${card.ref}`}
                                                        </div>

                                                        {card.motif_duplicate ? (
                                                            <div className="space-y-1.5">
                                                                <div className="text-xs text-gray-600 bg-white px-2 py-1 rounded-md border border-gray-100 truncate">
                                                                    <span className="font-medium">{t("common.motif", "Motif")}: </span>
                                                                    <span>{card.motif_duplicate[`nom_${currentLang}`] || card.motif_duplicate.nom_fr}</span>
                                                                </div>

                                                                {card.duplicate_amount && (
                                                                    <div className={`text-xs text-${tagColor}-600 bg-white px-2 py-1 rounded-md border border-${borderColor} truncate flex items-center gap-1`}>
                                                                        <span className="font-medium">{t("common.amount", "Montant carte")}: </span>
                                                                        <span>{parseFloat(card.duplicate_amount).toFixed(3)} {t("common.tnd")}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        ) : (
                                                            <div className="space-y-1.5">
                                                                <div className={`text-xs text-${tagColor}-600 bg-white px-2 py-1 rounded-md border border-${borderColor} truncate flex items-center gap-1`}>
                                                                    <CheckIcon className="w-3 h-3" />
                                                                    <span>{t("common.originalCard", "Carte originale")}</span>
                                                                </div>
                                                                {card.duplicate_amount && (
                                                                    <div className={`text-xs text-${tagColor}-600 bg-white px-2 py-1 rounded-md border border-${borderColor} truncate flex items-center gap-1`}>
                                                                        <span className="font-medium">{t("common.amount", "Montant carte")}: </span>
                                                                        <span>{parseFloat(card.duplicate_amount).toFixed(3)} {t("common.tnd")}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            );
                                        })
                                    }
                                </div>
                            ) : (
                                <div className="flex items-center justify-center gap-2 py-3 text-amber-600 bg-amber-50 rounded-md border border-amber-200">
                                    <AlertTriangleIcon className="w-4 h-4" />
                                    <span>{t("common.noCardInfo", "Aucune information de carte disponible")}</span>
                                </div>
                            )}
                        </div>
                    )}


                    {/* Section Paiement recap */}
                    <div className="px-6 pt-6 bg-white rounded-2xl border border-gray-200 w-full">
                        <div className="flex items-center justify-between mb-5">
                            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                <div className={`p-2.5 rounded-lg mr-3 ${
                                    abnRecord?.status === "PAYED"
                                        ? "bg-gradient-to-r from-green-50 to-green-100 text-green-600"
                                        : abnRecord?.status === "PENDING"
                                            ? "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-600"
                                            : "bg-gradient-to-r from-red-50 to-red-100 text-red-600"
                                }`}>
                                    <CreditCardIcon className="w-5 h-5"/>
                                </div>
                                <span className="text-gray-800">
                                    {t("manage_newSubs.payment", "Paiement")}
                                </span>
                            </h3>

                            <div className={`px-4 py-1.5 rounded-full text-sm font-medium shadow-sm ${
                                abnRecord?.status === "PAYED"
                                    ? "bg-gradient-to-r from-green-50 to-green-100 text-green-700 border border-green-200"
                                    : abnRecord?.status === "PENDING"
                                        ? "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 border border-gray-200"
                                        : "bg-gradient-to-r from-red-50 to-red-100 text-red-700 border border-red-200"
                            }`}>
                                {abnRecord?.status === "NOTPAYED" ? t("manage_newSubs.notPaid") :
                                 abnRecord?.status === "PAYED" ? t("manage_newSubs.alreadyPaid") :
                                 t("manage_newSubs.pending")}
                            </div>
                        </div>

                        {/* Carte de paiement stylisée avec détails */}
                        <div className={`${
                            abnRecord?.status === "PAYED"
                                ? "bg-white border-l-4 border-green-500"
                                : abnRecord?.status === "PENDING"
                                    ? "bg-white border-l-4 border-gray-500"
                                    : "bg-white border-l-4 border-red-500"
                        } rounded-xl p-6 mb-3 shadow-sm transition-all duration-300 relative overflow-hidden`}>
                            <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                                <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" className={`${
                                    abnRecord?.status === "PAYED" ? "text-green-700" : abnRecord?.status === "PENDING" ? "text-gray-700" : "text-red-700"
                                }`}>
                                    <path fill="currentColor" d="M47.1,-61.5C62.2,-53.9,76.5,-41.7,83.1,-25.9C89.8,-10.1,88.9,9.3,81.5,25.3C74.1,41.3,60.2,53.9,44.7,62.3C29.1,70.7,11.9,74.9,-4.6,80.6C-21.1,86.3,-37,93.5,-51.6,89.6C-66.2,85.7,-79.5,70.7,-85.3,53.7C-91.1,36.7,-89.5,17.8,-85.9,1.1C-82.3,-15.7,-76.8,-30.3,-67.1,-41.7C-57.4,-53.1,-43.5,-61.3,-29.4,-69C-15.3,-76.7,-1.1,-84,11.9,-79.8C24.9,-75.6,32,-69.1,47.1,-61.5Z" transform="translate(100 100)" />
                                </svg>
                            </div>

                            <div className="flex justify-between items-center mb-5 relative">
                                <div>
                                    <p className="text-gray-600 text-sm font-medium">{t("manage_newSubs.totalToPay", "Total à payer")}</p>
                                    <div className={`text-3xl font-bold mt-1 flex items-center ${
                                        abnRecord?.status === "PAYED"
                                            ? "text-green-700"
                                            : abnRecord?.status ==="PENDING"
                                            ? "text-gray-700"
                                            : "text-red-700"
                                    }`}>
                                        {abnRecord?.status === "PAYED"
                                            ? (() => {
                                                const paymentDetails = abnRecord?.latestTransaction?.payment_details
                                                    ? JSON.parse(abnRecord.latestTransaction.payment_details)
                                                    : null;
                                                return paymentDetails
                                                    ? `${parseFloat(parseFloat(paymentDetails.total_amount_with_discount).toFixed(1)).toFixed(3) || parseFloat(parseFloat(paymentDetails.total_amount_with_discount).toFixed(1)).toFixed(3)} TND`
                                                    : "0 TND";
                                            })()
                                            : abnRecord?.status === "NOTPAYED" || abnRecord?.status === "PENDING" ? formattedFinalAmount
                                            : "-"
                                        }
                                    </div>
                                </div>
                                <div className={`font-bold text-xl tracking-wider opacity-80 ${
                                    abnRecord?.status === "PAYED"
                                        ? "text-green-700"
                                        : abnRecord?.status === "PENDING"
                                        ? "text-gray-700"
                                        : "text-red-700"
                                }`}>
                                    SRTGN
                                </div>
                            </div>


                            <div className="bg-white rounded-lg p-4 animate-fadeIn shadow-sm border border-gray-200">

                                {
                                    abnRecord?.status !== "PENDING" && (
                                        <div className="flex items-center gap-2 text-gray-700 font-medium mb-3">
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className={`${
                                                abnRecord?.status === "PAYED" ? "text-green-600" : "text-red-600"
                                            }`}>
                                                <path d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M16.5 7.58008V8.58008C16.5 9.40008 15.83 10.0801 15 10.0801H9C8.18 10.0801 7.5 9.41008 7.5 8.58008V7.58008C7.5 6.76008 8.17 6.08008 9 6.08008H15C15.83 6.08008 16.5 6.75008 16.5 7.58008Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M8.13599 14H8.14799" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M12 14H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M15.864 14H15.876" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M8.13599 17.5H8.14799" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M12 17.5H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M15.864 17.5H15.876" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            <span>{t("manage_newSubs.paymentDetails", "Détails du paiement")}</span>
                                        </div>
                                    )
                                }


                                {/*---------------------------------------------------------------*
                                *  - PAYED SUBS
                                *--------------------------------------------------------------*/}

                                {abnRecord?.status === "PAYED" && (
                                    <>
                                        {(() => {
                                            const paymentDetails = abnRecord?.latestTransaction?.payment_details
                                                ? JSON.parse(abnRecord?.latestTransaction?.payment_details)
                                                : null;

                                            return paymentDetails ? (
                                                <div className="space-y-2">
                                                    {paymentDetails.majoration ? (
                                                        <>
                                                            <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                                <span className="text-gray-600 text-sm"> {t("manage_newSubs.initialAmount", "Montant initial: ")}</span>
                                                                <span className="text-gray-800 text-sm font-medium">
                                                                    {parseFloat(paymentDetails.majoration.original_amount).toFixed(3)} TND
                                                                </span>
                                                            </div>

                                                            <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors bg-amber-50">
                                                                <div className="flex items-center gap-1.5">
                                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-amber-600">
                                                                        <path d="M12 8V13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                                        <path d="M12 16V16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                                        <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                                    </svg>
                                                                    <span className="text-gray-700 text-sm">Majoration (x{paymentDetails.majoration.value}):</span>
                                                                </div>
                                                                <span className="text-amber-700 text-sm font-medium">
                                                                    {(paymentDetails.majoration.majored_amount - paymentDetails.majoration.original_amount).toFixed(3)} TND
                                                                </span>
                                                            </div>
                                                        </>
                                                    ) : (
                                                        <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                            <span className="text-gray-600 text-sm">Montant de base:</span>
                                                            <span className="text-gray-800 text-sm font-medium">{parseFloat(paymentDetails.amount_base).toFixed(3)} {t("common.tnd")}</span>
                                                        </div>
                                                    )}

                                                    {paymentDetails.card_fee > 0 && (
                                                        <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                            <div className="flex items-center gap-1.5">
                                                                <span className="text-gray-600 text-sm">{t("manage_newSubs.cardFee", "Frais de carte")}:</span>
                                                            </div>
                                                            <span className="text-gray-800 text-sm font-medium">{parseFloat(paymentDetails.card_fee).toFixed(3)} {t("common.tnd")}</span>
                                                        </div>
                                                    )}

                                                    {paymentDetails.custom_discount_percentage > 0 && (
                                                        <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                            <div className="flex items-center gap-1.5">
                                                                <span className="text-gray-600 text-sm">
                                                                    Remise personnalisée ({paymentDetails.custom_discount_percentage}%):
                                                                </span>
                                                            </div>
                                                            <span className="text-blue-600 text-sm font-medium">-{parseFloat(paymentDetails.custom_discount_amount).toFixed(3)} {t("common.tnd")}</span>
                                                        </div>
                                                    )}

                                                    {paymentDetails.discount_percentage > 0 && (
                                                        <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                            <div className="flex items-center gap-1.5">
                                                                <span className="text-gray-600 text-sm">
                                                                    Remise générale ({paymentDetails.discount_percentage}%):
                                                                </span>
                                                            </div>
                                                            <span className="text-green-600 text-sm font-medium">-{parseFloat(paymentDetails.discount_amount).toFixed(3)} {t("common.tnd")}</span>
                                                        </div>
                                                    )}

                                                    <div className="flex justify-between items-center py-2 px-3 mt-2 bg-gray-50 rounded-lg border border-gray-100">
                                                        <span className="text-gray-700 font-medium">Montant final:</span>
                                                        <span className="text-red-600 font-bold">
                                                            {parseFloat(parseFloat(paymentDetails.total_amount_with_discount).toFixed(1)).toFixed(3) || parseFloat(parseFloat(paymentDetails.total_amount_with_discount).toFixed(1)).toFixed(3)} {t("common.tnd")}
                                                        </span>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="text-center py-4 text-gray-500 bg-gray-50 rounded-lg">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mx-auto mb-2 text-gray-400">
                                                        <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                        <path d="M12 8V13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                        <path d="M11.9941 16H12.0031" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                    </svg>
                                                    {t("common.noPaymentDetails")}
                                                </div>
                                            );
                                        })()}
                                    </>
                                )}


                                {/*---------------------------------------------------------------*
                                *  - NOT PAYED SUBS
                                *--------------------------------------------------------------*/}
                                {abnRecord?.status === "NOTPAYED" && (
                                    <div className="space-y-2">
                                        {abnRecord?.majoration?.applied ? (
                                            <>
                                                <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                    <span className="text-gray-600 text-sm">Montant initial:</span>
                                                    <span className="text-gray-800 text-sm font-medium">
                                                        {parseFloat(abnRecord.majoration.original_amount).toFixed(3)} {t("common.tnd")}
                                                    </span>
                                                </div>

                                                <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors bg-amber-50">
                                                    <div className="flex items-center gap-1.5">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-amber-600">
                                                            <path d="M12 8V13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                            <path d="M12 16V16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                            <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                        </svg>
                                                        <span className="text-gray-700 text-sm">Majoration (x{abnRecord.majoration.value}):</span>
                                                    </div>
                                                    <span className="text-amber-700 text-sm font-medium">
                                                        {(abnRecord.majoration.majored_amount - abnRecord.majoration.original_amount).toFixed(3)} TND
                                                    </span>
                                                </div>
                                            </>
                                        ) : (
                                            <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                <span className="text-gray-600 text-sm">Montant de base:</span>
                                                <span className="text-gray-800 text-sm font-medium">{formattedBaseAmount}</span>
                                            </div>
                                        )}

                                        <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                            <div className="flex items-center gap-1.5">
                                                <span className="text-gray-600 text-sm">Frais de carte:</span>
                                            </div>
                                            <span className="text-gray-800 text-sm font-medium">{formattedCardFeeAmount}</span>
                                        </div>

                                        {isCustomDiscountEnabled && (
                                            <>
                                                {previewCustomDiscountAmount !== null && customDiscountPercentage && !appliedCustomDiscountPercentage && (
                                                    <div className="flex justify-between items-center py-1.5 px-2 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors border border-blue-100">
                                                        <div className="flex items-center gap-1.5">
                                                            <span className="text-gray-600 text-sm">Remise personnalisée (aperçu) ({customDiscountPercentage ? parseFloat(customDiscountPercentage).toFixed(2) : "0"}%):</span>
                                                        </div>
                                                        <span className="text-blue-600 text-sm font-medium">
                                                            -{previewCustomDiscountAmount.toFixed(3)} TND
                                                        </span>
                                                    </div>
                                                )}

                                                {appliedCustomDiscountPercentage && (
                                                    <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                        <div className="flex items-center gap-1.5">
                                                            <span className="text-gray-600 text-sm">Remise personnalisée ({appliedCustomDiscountPercentage ? parseFloat(appliedCustomDiscountPercentage).toFixed(2) : "0"}%):</span>
                                                        </div>
                                                        <span className="text-blue-600 text-sm font-medium">
                                                            -{customDiscountAmount ? customDiscountAmount.toFixed(3) : "0.000"} TND
                                                        </span>
                                                    </div>
                                                )}
                                            </>
                                        )}

                                        {applicableDiscount && (
                                            <div className="flex justify-between items-center py-1.5 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                                                <div className="flex items-center gap-1.5">
                                                    <span className="text-gray-600 text-sm">Remise générale ({discountPercentage}%):</span>
                                                </div>
                                                <span className="text-green-600 text-sm font-medium">-{formattedDiscountAmount}</span>
                                            </div>
                                        )}

                                        <div className="flex justify-between items-center py-2 px-3 mt-2 bg-gray-50 rounded-lg border border-gray-100">
                                            <span className="text-gray-700 font-medium">Montant final:</span>
                                            <span className="text-red-600 font-bold">
                                                {formattedFinalAmount}
                                            </span>
                                        </div>
                                    </div>
                                )}


                                {/*---------------------------------------------------------------*
                                *  - PENDING SUBS (MODIFIED WHILE IT S PAYED)
                                *--------------------------------------------------------------*/}
                                {
                                    abnRecord?.status === "PENDING" && (
                                        <div className="mt-4 mb-2">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                {/* Colonne de gauche: Détails originaux */}
                                                <div className="bg-white rounded-lg p-4 relative overflow-hidden">
                                                    {/* Effet de halo externe */}
                                                    <div className="absolute -inset-1 z-0 opacity-70"

                                                    />
                                                    <div className="flex items-center gap-2 mb-3 relative z-10">
                                                        <div className="p-1.5 rounded-md bg-gray-100">
                                                            <CheckIcon className="font-semibold text-gray-800" />
                                                        </div>
                                                        <h3 className="font-medium text-gray-800">{t("manage_newSubs.originalPayment", "Paiement original")}</h3>
                                                    </div>

                                                    {(() => {
                                                        const paymentDetails = abnRecord?.latestTransaction?.payment_details
                                                            ? JSON.parse(abnRecord?.latestTransaction?.payment_details)
                                                            : null;

                                                        return paymentDetails ? (
                                                            <div className="space-y-2 relative z-10 bg-white bg-opacity-90 rounded-lg p-3 mt-3">
                                                                <h4 className="text-gray-600 font-semibold text-sm mb-2 pb-1">
                                                                    {t("manage_newSubs.paymentDetails", "Détails du paiement")}
                                                                </h4>

                                                                {/* Montant de base */}
                                                                <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors backdrop-blur-sm border border-gray-100">
                                                                    <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.baseAmount", "Montant de base")}</span>
                                                                    <span className="text-gray-700 text-sm font-bold">
                                                                        {parseFloat(paymentDetails.amount_base).toFixed(3)} {t("common.tnd")}
                                                                    </span>
                                                                </div>

                                                                {/* Frais de carte */}
                                                                {paymentDetails.card_fee > 0 && (
                                                                    <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors backdrop-blur-sm border border-gray-100">
                                                                        <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.cardFee", "Frais de carte")}</span>
                                                                        <span className="text-gray-700 text-sm font-bold">
                                                                            {parseFloat(paymentDetails.card_fee).toFixed(3)} {t("common.tnd")}
                                                                        </span>
                                                                    </div>
                                                                )}

                                                                {/* Remises */}
                                                                {paymentDetails.discount_percentage > 0 && (
                                                                    <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors backdrop-blur-sm border border-gray-100">
                                                                        <div className="flex items-center gap-1">
                                                                            <PercentIcon className="w-3.5 h-3.5 text-green-600" />
                                                                            <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.generalDiscount", "Remise générale")} ({paymentDetails.discount_percentage}%):</span>
                                                                        </div>
                                                                        <span className="text-green-600 text-sm font-bold">
                                                                            -{parseFloat(paymentDetails.discount_amount).toFixed(3)} {t("common.tnd")}
                                                                        </span>
                                                                    </div>
                                                                )}

                                                                {paymentDetails.custom_discount_percentage > 0 && (
                                                                    <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors backdrop-blur-sm border border-gray-100">
                                                                        <div className="flex items-center gap-1">
                                                                            <PercentIcon className="w-3.5 h-3.5 text-blue-600" />
                                                                            <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.customDiscount", "Remise personnalisée")} ({paymentDetails.custom_discount_percentage}%):</span>
                                                                        </div>
                                                                        <span className="text-blue-600 text-sm font-bold">
                                                                            -{parseFloat(paymentDetails.custom_discount_amount).toFixed(3)} {t("common.tnd")}
                                                                        </span>
                                                                    </div>
                                                                )}

                                                                {/* Montant final */}
                                                                <div className="flex justify-between items-center py-3 px-4 mt-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200 shadow-sm">
                                                                    <div className="flex items-center gap-2">
                                                                        <CreditCardIcon className="font-semibold text-gray-800" />
                                                                        <span className="text-gray-700 font-semibold">{t("manage_newSubs.finalAmount", "Montant final")}</span>
                                                                    </div>
                                                                    <div className="flex items-center">
                                                                        <span className="text-green-600 font-bold text-base">
                                                                            {parseFloat(parseFloat(paymentDetails.total_amount_with_discount).toFixed(1)).toFixed(3)} {t("common.tnd")}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <div className="text-center py-4 text-gray-500 bg-gray-50 rounded-lg border border-gray-200 mt-3">
                                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mx-auto mb-2 text-gray-400">
                                                                    <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                                    <path d="M12 8V13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                                    <path d="M11.9941 16H12.0031" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                                </svg>
                                                                {t("common.noPaymentDetails")}
                                                            </div>
                                                        );
                                                    })()}
                                                </div>

                                                {/* Colonne de droite: Nouveaux détails */}
                                                <div className="bg-white rounded-lg border border-gray-200 p-4 relative overflow-hidden"
                                                    style={{
                                                        animation: 'vibrate 0.5s infinite alternate ease-in-out',
                                                        animationPlayState: 'paused'
                                                    }}
                                                >
                                                    {/* Effet de halo externe */}
                                                    <div className="absolute -inset-1 z-0 opacity-70"
                                                        style={{
                                                            background: 'linear-gradient(45deg, rgba(220, 38, 38, 0.3), rgba(248, 113, 113, 0.2), rgba(220, 38, 38, 0.3))',
                                                            filter: 'blur(8px)',
                                                            animation: 'rotateGradient 8s infinite linear'
                                                        }}
                                                    />

                                                    {/* Effet de pulsation en arrière-plan */}
                                                    <div className="absolute inset-0 bg-red-50 opacity-40 z-0"
                                                        style={{
                                                            animation: 'pulseRedBackground 2s infinite ease-in-out',
                                                        }}
                                                    />

                                                    {/* Effet de bordure pulsante amélioré */}
                                                    <div className="absolute inset-0 rounded-lg z-0"
                                                        style={{
                                                            border: '2px solid rgba(220, 38, 38, 0.5)',
                                                            boxShadow: '0 0 15px rgba(220, 38, 38, 0.3), inset 0 0 8px rgba(220, 38, 38, 0.2)',
                                                            animation: 'pulseRedBorder 2s infinite ease-in-out',
                                                        }}
                                                    />

                                                    {/* Effet de rayonnement amélioré */}
                                                    <div className="absolute inset-0 z-0 opacity-20"
                                                        style={{
                                                            background: 'radial-gradient(circle at center, rgba(220, 38, 38, 0.6) 0%, transparent 70%)',
                                                            animation: 'pulseRadial 3s infinite ease-in-out',
                                                        }}
                                                    />

                                                    {/* Effet de particules */}
                                                    <div className="absolute inset-0 z-0 overflow-hidden">
                                                        <div className="absolute h-2 w-2 rounded-full bg-red-400 opacity-70"
                                                            style={{
                                                                top: '20%',
                                                                left: '10%',
                                                                animation: 'floatParticle 4s infinite ease-in-out'
                                                            }}
                                                        />
                                                        <div className="absolute h-1 w-1 rounded-full bg-red-300 opacity-60"
                                                            style={{
                                                                top: '70%',
                                                                left: '80%',
                                                                animation: 'floatParticle 3s infinite ease-in-out reverse'
                                                            }}
                                                        />
                                                        <div className="absolute h-1.5 w-1.5 rounded-full bg-red-500 opacity-50"
                                                            style={{
                                                                top: '40%',
                                                                left: '85%',
                                                                animation: 'floatParticle 5s infinite ease-in-out'
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="flex items-center justify-between mb-3 relative z-10">
                                                        <div className="flex items-center gap-2">
                                                            <div className="p-1.5 rounded-md bg-red-200">
                                                                <RefreshCwIcon className="w-4 h-4 text-red-600" />
                                                            </div>
                                                            <h3 className="font-medium text-gray-800">{t("manage_newSubs.newPayment", "Nouveau paiement")}</h3>
                                                        </div>
                                                        <div className="ml-2 px-3 py-1 bg-gradient-to-r from-red-500 to-red-600 rounded-full text-xs font-bold text-white relative shadow-md">
                                                            <span className="relative z-10 ml-2 tracking-wider uppercase">{t("messages.pending", "en attente")}</span>

                                                            {/* Effet de brillance qui se déplace */}
                                                            <div
                                                                className="absolute inset-0 overflow-hidden rounded-full"
                                                                style={{
                                                                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
                                                                    transform: 'skewX(-20deg)',
                                                                    animation: 'shimmer 2s infinite',
                                                                    zIndex: 5
                                                                }}
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="space-y-2 relative z-10 bg-white bg-opacity-90 rounded-lg p-3 mt-3 border border-red-200 shadow-inner">
                                                        <h4 className="text-red-600 font-semibold text-sm mb-2 border-b border-red-100 pb-1">
                                                            {t("manage_newSubs.pendingPaymentDetails", "Détails du paiement en attente")}
                                                        </h4>

                                                        {/* Montant de base */}
                                                        <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-red-50 transition-colors backdrop-blur-sm border border-red-100">
                                                            <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.baseAmount", "Montant de base")}</span>
                                                            <span className="text-red-700 text-sm font-bold">{formattedBaseAmount}</span>
                                                        </div>

                                                        {/* Frais de carte */}
                                                        <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-red-50 transition-colors backdrop-blur-sm border border-red-100">
                                                            <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.cardFee", "Frais de carte")}</span>
                                                            <span className="text-red-700 text-sm font-bold">{formattedCardFeeAmount}</span>
                                                        </div>

                                                        {/* Remises */}
                                                        {applicableDiscount && (
                                                            <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-red-50 transition-colors backdrop-blur-sm border border-red-100">
                                                                <div className="flex items-center gap-1">
                                                                    <PercentIcon className="w-3.5 h-3.5 text-green-600" />
                                                                    <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.generalDiscount", "Remise générale")} ({discountPercentage}%):</span>
                                                                </div>
                                                                <span className="text-green-600 text-sm font-bold">-{formattedDiscountAmount}</span>
                                                            </div>
                                                        )}

                                                        {isCustomDiscountEnabled && appliedCustomDiscountPercentage && (
                                                            <div className="flex justify-between items-center py-2 px-3 rounded-lg hover:bg-red-50 transition-colors backdrop-blur-sm border border-red-100">
                                                                <div className="flex items-center gap-1">
                                                                    <PercentIcon className="w-3.5 h-3.5 text-blue-600" />
                                                                    <span className="text-gray-700 text-sm font-medium">{t("manage_newSubs.customDiscount", "Remise personnalisée")} ({parseFloat(appliedCustomDiscountPercentage).toFixed(2)}%):</span>
                                                                </div>
                                                                <span className="text-blue-600 text-sm font-bold">
                                                                    -{customDiscountAmount ? customDiscountAmount.toFixed(3) : "0.000"} {t("common.tnd")}
                                                                </span>
                                                            </div>
                                                        )}

                                                        {/* Montant final */}
                                                        <div className="flex justify-between items-center py-3 px-4 mt-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg border border-red-200 shadow-sm">
                                                            <div className="flex items-center gap-2">
                                                                <CreditCardIcon className="w-4 h-4 text-red-600" />
                                                                <span className="text-red-700 font-semibold">{t("manage_newSubs.finalAmount", "Montant final")}</span>
                                                            </div>
                                                            <div className="flex items-center">
                                                                <span className="text-red-600 font-bold text-base">{formattedFinalAmount}</span>
                                                                <div className="ml-1 w-2 h-2 rounded-full bg-red-500 animate-ping"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Différence de montant */}
                                            {(() => {
                                                const paymentDetails = abnRecord?.latestTransaction?.payment_details
                                                    ? JSON.parse(abnRecord?.latestTransaction?.payment_details)
                                                    : null;

                                                if (paymentDetails && getFinalAmount) {
                                                    const oldAmount = parseFloat(paymentDetails.total_amount_with_discount);
                                                    const newAmount = getFinalAmount;
                                                    const difference = newAmount - oldAmount;

                                                    return (
                                                        <div className={`mt-4 p-3 rounded-lg border ${
                                                            difference > 0
                                                                ? "bg-gray-50 border-gray-200"
                                                                : difference < 0
                                                                    ? "bg-green-50 border-green-200"
                                                                    : "bg-gray-50 border-gray-200"
                                                        }`}>
                                                            <div className="flex justify-between items-center">
                                                                <span className="font-medium">{t("manage_newSubs.amountDifference", "Différence de montant")}:</span>
                                                                <span className={`font-bold ${
                                                                    difference > 0
                                                                        ? "text-gray-600"
                                                                        : difference < 0
                                                                            ? "text-green-600"
                                                                            : "text-gray-600"
                                                                }`}>
                                                                    {difference > 0 ? "+" : ""}{difference.toFixed(3)} {t("common.tnd")}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    );
                                                }
                                                return null;
                                            })()}
                                        </div>
                                    )
                                }
                            </div>
                        </div>


                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={confirmPayment}
                            className="space-y-2 form-inputs"
                        >
                            {/* Section Remise Personnalisée */}
                            {(abnRecord?.status === "NOTPAYED" || abnRecord?.status === "PENDING")  && (
                                <div className="bg-white rounded-2xl p-6 border border-gray-200">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="p-2.5 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 text-blue-600 shadow-sm">
                                                <PercentIcon className="w-5 h-5" />
                                            </div>
                                            <span className="text-gray-800 font-medium text-lg">
                                                {t("manage_newSubs.labels.customDiscount", "Remise personnalisée")}
                                            </span>
                                        </div>
                                        <Form.Item
                                            name="is_custom_discount"
                                            valuePropName="checked"
                                            className="mb-0"
                                        >
                                            <Switch
                                                disabled={viewMode}
                                                onChange={(checked) => {
                                                    setIsCustomDiscountEnabled(checked);
                                                    if (!checked) {
                                                        setCustomDiscountPercentage("");
                                                        setAppliedCustomDiscountPercentage("");
                                                        setPreviewCustomDiscountAmount(null);
                                                        form.setFieldsValue({ custom_discount_percentage: "" });
                                                    }
                                                }}
                                                className={`${
                                                    isCustomDiscountEnabled ? '!bg-gradient-to-r !from-blue-400 !to-blue-500' : 'bg-gray-300'
                                                } transition-colors duration-300`}
                                                checkedChildren={t("common.yes")}
                                                unCheckedChildren={t("common.no")}
                                            />
                                        </Form.Item>
                                    </div>

                                    {isCustomDiscountEnabled && (
                                        <div className={`mt-5 p-4 rounded-lg bg-gray-50 border border-gray-100 transition-all duration-500 ${isCustomDiscountEnabled ? 'opacity-100' : 'opacity-0'}`}>
                                            <Row gutter={[16, 16]} align="bottom">
                                                <Col xs={24} sm={16}>
                                                    <Form.Item
                                                        label={
                                                            <span className="text-gray-700 font-medium flex items-center gap-2">
                                                                {t("manage_newSubs.labels.customDiscountPercentage", "Pourcentage de remise (%)")}
                                                            </span>
                                                        }
                                                        name="custom_discount_percentage"
                                                        className="mb-0"
                                                    >
                                                        <Input
                                                            type="number"
                                                            placeholder="0.00"
                                                            disabled={viewMode}
                                                            value={customDiscountPercentage}
                                                            onChange={(e) => setCustomDiscountPercentage(e.target.value)}
                                                            className="rounded-lg border-gray-300 hover:border-gray-400 focus:border-gray-500 transition-colors"
                                                            suffix="%"
                                                        />
                                                    </Form.Item>
                                                </Col>
                                                <Col xs={24} sm={8}>
                                                    <Button
                                                        onClick={handleApplyCustomDiscount}
                                                        disabled={viewMode || !customDiscountPercentage}
                                                        className="rounded-xl !bg-gradient-to-r from-blue-500 to-blue-600 border-0 flex items-center gap-2 w-full h-[40px] justify-center duration-300 transform hover:-translate-y-1"
                                                    >
                                                        <CheckIcon className="w-4 h-4 text-white" />
                                                        <span className="text-white font-medium">{t("common.apply", "Appliquer")}</span>
                                                    </Button>
                                                </Col>
                                            </Row>
                                            {appliedCustomDiscountPercentage && (
                                                <div className="mt-3 flex items-center gap-2 text-blue-600 bg-gray-50 p-2 rounded-lg border border-gray-100">
                                                    <CheckCircleOutlined />
                                                    <span className="text-sm">{t("manage_newSubs.customDiscountApplied", "Remise personnalisée appliquée")}: {parseFloat(appliedCustomDiscountPercentage).toFixed(2)}%</span>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Section Affaire Sociale */}
                            {(abnRecord?.status === "NOTPAYED" || abnRecord?.status === "PENDING") && (
                                <div className="bg-white rounded-2xl p-6 border border-gray-200 duration-300">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="p-2.5 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 text-gray-600 shadow-sm">
                                                <UsergroupAddOutlined style={{ fontSize: '18px' }} />
                                            </div>
                                            <span className="text-gray-800 font-medium text-lg">
                                                {t("manage_newSubs.labels.socialAffair")}
                                            </span>
                                        </div>
                                        <Form.Item
                                            name="is_social_affair"
                                            valuePropName="checked"
                                            className="mb-0"
                                        >
                                            <Switch
                                                disabled={viewMode}
                                                onChange={(checked) => setIsSocialAffair(checked)}
                                                className={`${
                                                    isSocialAffair ? '!bg-gradient-to-r !from-gray-500 !to-gray-600' : 'bg-gray-300'
                                                } transition-colors duration-300`}
                                                checkedChildren={t("common.yes")}
                                                unCheckedChildren={t("common.no")}
                                            />
                                        </Form.Item>
                                    </div>

                                    {isSocialAffair && (
                                        <div className={`mt-5 p-4 rounded-lg bg-gray-50 border border-gray-100 transition-all duration-500 ${isSocialAffair ? 'opacity-100' : 'opacity-0'}`}>
                                            <Row gutter={[16, 16]} align="bottom">
                                                <Col xs={24} sm={16}>
                                                    <Form.Item
                                                        label={
                                                            <span className="text-gray-700 font-medium flex items-center gap-2">
                                                                {t("manage_newSubs.labels.parentCin")}
                                                            </span>
                                                        }
                                                        name="cin_parent"
                                                        rules={[{
                                                            required: true,
                                                            message: t("manage_newSubs.errors.cinRequired")
                                                        }]}
                                                        className="mb-0"
                                                    >
                                                        <Input
                                                            placeholder={t("manage_newSubs.placeholders.parentCin")}
                                                            disabled={viewMode}
                                                            className="rounded-lg border-gray-300 hover:border-gray-400 focus:border-gray-500 transition-colors"
                                                        />
                                                    </Form.Item>
                                                </Col>
                                                <Col xs={24} sm={8}>
                                                    <Button
                                                        onClick={handleVerify}
                                                        disabled={viewMode}
                                                        loading={loading}
                                                        className="rounded-xl !bg-gradient-to-r from-gray-600 to-gray-700 border-0 flex items-center gap-2 w-full h-[40px] justify-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                                                    >
                                                        <SearchIcon className="w-4 h-4 text-white" />
                                                        <span className="text-white font-medium">{t("common.verify")}</span>
                                                    </Button>
                                                </Col>
                                            </Row>

                                            {isSocialAffairVerified && (
                                                <div className="mt-3 flex items-center gap-2 text-green-600 bg-gray-50 p-2 rounded-lg border border-gray-100">
                                                    <CheckCircleOutlined />
                                                    <span className="text-sm">{t("messages.verified_successfully", "Vérifié avec succès")}</span>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Section Méthode de Paiement */}
                            {(!isSocialAffair && !isSocialAffairVerified && (abnRecord?.status === "NOTPAYED" || abnRecord?.status === "PENDING")) && (
                                <div className="bg-white rounded-2xl p-6 border border-gray-200 duration-300">
                                    <div className="flex items-center gap-3 mb-4">
                                        <div className="p-2.5 rounded-lg bg-gradient-to-r from-red-50 to-red-100 text-red-600 shadow-sm">
                                            <CreditCardIcon className="w-5 h-5" />
                                        </div>
                                        <span className="text-gray-800 font-medium text-lg">
                                            {t("manage_newSubs.labels.paymentMethod")}
                                        </span>
                                    </div>

                                    <Form.Item
                                        name="payment_method_id"
                                        rules={[{
                                            required: true,
                                            message: t("manage_newSubs.errors.selectPaymentMethod")
                                        }]}
                                        className="mb-0"
                                    >
                                        <Select
                                            disabled={abnRecord?.status === "PAYED"}
                                            placeholder={t("manage_newSubs.placeholders.selectPaymentMethod")}
                                            className="w-full"
                                            size="large"
                                            dropdownStyle={{ borderRadius: '0.75rem' }}
                                            suffixIcon={<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-400">
                                                <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>}
                                        >
                                            {paymentMethods?.map((method: any) => (
                                                <Select.Option key={method.id} value={method.id}>
                                                    <div className="flex items-center gap-2">
                                                        <CreditCard className="w-4 h-4 text-red-500" />
                                                        {method[`nom_${currentLang}`]}
                                                    </div>
                                                </Select.Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </div>
                            )}

                           {/* Section Bouton de Confirmation */}
                           {
                                (abnRecord?.status === "NOTPAYED" || abnRecord?.status === "PENDING") && (
                                    <div className="mt-2">
                                        <Form.Item className="mb-4">
                                                    <div className="relative">
                                                        <Button
                                                            onClick={() => {
                                                                form.validateFields()
                                                                    .then(values => confirmPayment(values))
                                                                    .catch(error => console.error('Validation failed:', error));
                                                            }}
                                                            loading={externalLoading || paymentLoading}
                                                            className={`w-full h-16 font-bold rounded-xl flex items-center !hover:bg-red-600 justify-center gap-3 !bg-red-600 border-0 relative z-10`}

                                                        >
                                                            <div className="flex items-center gap-3 text-white">
                                                                <span className="text-white !bg-red-600 uppercase">{t("manage_newSubs.confirmPayment")}</span>
                                                            </div>
                                                        </Button>
                                                    </div>
                                        </Form.Item>
                                    </div>
                                )
                            }
                        </Form>
                    </div>
                </div>
            </Spin>
        </Modal>
        </>
    );
}

export default PaymentModal;
